# -*- coding: utf-8 -*-
"""
家樂福離線資料系統基本測試

這個檔案包含基本的測試，確保 CI/CD 流程可以正常執行。
支援 pytest 和 unittest 兩種執行方式。
"""

import unittest
import pytest
import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TestBasicFunctionality(unittest.TestCase):
    """基本功能測試"""

    def test_python_environment(self):
        """測試 Python 環境是否正確設定"""
        self.assertGreaterEqual(sys.version_info, (3, 8), "Python 版本應該 >= 3.8")

    def test_required_modules_importable(self):
        """測試必要的模組是否可以匯入"""
        # 在 CI 環境中測試完整的依賴，本地環境只測試基本模組
        if os.getenv('GITHUB_ACTIONS') or os.getenv('CI'):
            try:
                import pandas
                import google.cloud.bigquery
                import google.cloud.functions
            except ImportError as e:
                self.fail(f"無法匯入必要模組: {e}")
        else:
            # 本地環境只測試基本 Python 功能
            try:
                import json
                # 如果有安裝 pandas，也測試一下
                try:
                    import pandas
                except ImportError:
                    pass  # 本地環境可能沒有安裝，這是正常的
            except ImportError as e:
                self.fail(f"無法匯入基本模組: {e}")

    def test_environment_variables(self):
        """測試環境變數設定（在 CI 環境中）"""
        if os.getenv('GITHUB_ACTIONS') or os.getenv('CI'):
            # 在 CI 環境中檢查必要的環境變數
            has_credentials = (os.getenv('GOOGLE_APPLICATION_CREDENTIALS') is not None or
                             os.getenv('GCLOUD_SERVICE_KEY') is not None)
            self.assertTrue(has_credentials, "CI 環境應該設定 Google Cloud 認證")


class TestProjectStructure(unittest.TestCase):
    """專案結構測試"""

    def test_src_directory_exists(self):
        """測試 src 目錄是否存在"""
        src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
        self.assertTrue(os.path.exists(src_path), "src 目錄應該存在")

    def test_terraform_directory_exists(self):
        """測試 terraform 目錄是否存在"""
        terraform_path = os.path.join(os.path.dirname(__file__), '..', 'terraform')
        self.assertTrue(os.path.exists(terraform_path), "terraform 目錄應該存在")

    def test_makefile_exists(self):
        """測試 Makefile 是否存在"""
        makefile_path = os.path.join(os.path.dirname(__file__), '..', 'Makefile')
        self.assertTrue(os.path.exists(makefile_path), "Makefile 應該存在")


class TestConfigurationFiles(unittest.TestCase):
    """配置檔案測試"""

    def test_requirements_txt_exists(self):
        """測試 requirements.txt 是否存在"""
        req_path = os.path.join(os.path.dirname(__file__), '..', 'requirements.txt')
        self.assertTrue(os.path.exists(req_path), "requirements.txt 應該存在")

    def test_terraform_main_exists(self):
        """測試主要的 Terraform 檔案是否存在"""
        main_tf_path = os.path.join(os.path.dirname(__file__), '..', 'terraform', 'main.tf')
        self.assertTrue(os.path.exists(main_tf_path), "terraform/main.tf 應該存在")


# 標記測試類型（用於選擇性執行）
pytestmark = pytest.mark.unit


if __name__ == "__main__":
    # 允許直接執行此測試檔案
    unittest.main(verbosity=2)
