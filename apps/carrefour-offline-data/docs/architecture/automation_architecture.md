# 家樂福受眾媒合系統自動化架構設計

## 📋 系統概述

本文檔描述家樂福受眾媒合系統的完整自動化架構，包括每日自動複製、自動化分析、監控告警等功能的設計和實作方案。

## 🏗️ 整體架構

### 核心組件

1. **資料複製服務** (Data Replication Service)
2. **受眾媒合服務** (Audience Matching Service)
3. **統計分析服務** (Statistics Analysis Service)
4. **監控告警服務** (Monitoring & Alerting Service)
5. **資料品質檢查服務** (Data Quality Service)

### 技術棧

- **運算平台**: Google Cloud Functions (Gen 2)
- **排程系統**: Google Cloud Scheduler
- **資料儲存**: Google BigQuery
- **訊息佇列**: Google Pub/Sub
- **監控系統**: Google Cloud Monitoring
- **基礎設施**: Terraform (Infrastructure as Code)

## 🔄 自動化流程設計

### 每日執行流程

```mermaid
graph TD
    A[Cloud Scheduler<br/>每日凌晨 1:00] --> B[觸發資料複製]
    B --> C[檢查來源資料]
    C --> D{資料是否可用}
    D -->|否| E[發送告警<br/>等待重試]
    D -->|是| F[執行資料複製]

    F --> G[複製完成通知]
    G --> H[觸發受眾媒合]
    H --> I[執行媒合邏輯]
    I --> J[生成受眾標籤]
    J --> K[寫入目標表格]

    K --> L[觸發統計分析]
    L --> M[執行資料分析]
    M --> N[生成統計報告]
    N --> O[資料品質檢查]

    O --> P{品質檢查通過}
    P -->|否| Q[發送品質告警]
    P -->|是| R[發送成功通知]

    E --> S[重試機制<br/>最多3次]
    S --> T{重試成功}
    T -->|是| F
    T -->|否| U[發送失敗告警]

    Q --> V[記錄問題日誌]
    R --> W[更新監控儀表板]
    U --> V
    V --> X[流程結束]
    W --> X
```

## 📊 服務詳細設計

### 1. 資料複製服務

**功能**: 每日自動複製家樂福線下交易資料到 tagtoo-tracking 專案

**實作方式**:

```python
# Cloud Function: data-replication-service
def replicate_carrefour_data(event, context):
    """
    每日資料複製主程式
    """
    # 1. 檢查來源資料可用性
    # 2. 執行增量複製
    # 3. 驗證複製完整性
    # 4. 發送完成通知
```

**排程**: 每日凌晨 1:00 (Asia/Taipei)

**監控指標**:

- 複製資料量
- 複製耗時
- 錯誤率
- 資料延遲

### 2. 受眾媒合服務

**功能**: 執行線下購買資料與線上事件資料的媒合

**實作方式**:

```python
# Cloud Function: audience-matching-service
def execute_audience_matching(event, context):
    """
    受眾媒合主程式
    """
    # 1. 建立用戶對應表
    # 2. 查詢線下購買資料
    # 3. 生成階層式標籤
    # 4. 寫入目標表格
```

**觸發條件**: 資料複製完成後自動觸發

**監控指標**:

- 媒合用戶數
- 生成標籤數
- 處理耗時
- 成功率

### 3. 統計分析服務

**功能**: 分析每日寫入的受眾資料狀態

**實作方式**:

```python
# Cloud Function: statistics-analysis-service
def analyze_daily_statistics(event, context):
    """
    統計分析主程式
    """
    # 1. 分析基本統計
    # 2. 分析標籤分佈
    # 3. 分析商品分類
    # 4. 生成分析報告
```

**觸發條件**: 受眾媒合完成後自動觸發

**輸出**:

- 每日統計報告 (JSON)
- 監控儀表板更新
- 異常告警

### 4. 監控告警服務

**功能**: 監控系統運行狀態並發送告警

**監控項目**:

- 服務可用性
- 資料處理量
- 錯誤率
- 執行時間
- 資料品質

**告警規則**:

```yaml
alerts:
  - name: "資料複製失敗"
    condition: "複製服務連續失敗 > 2 次"
    severity: "critical"

  - name: "受眾媒合異常"
    condition: "媒合用戶數 < 預期的 50%"
    severity: "warning"

  - name: "資料品質下降"
    condition: "品質評分 < 80%"
    severity: "warning"
```

## 🔧 技術實作

### Cloud Functions 配置

```yaml
# 資料複製服務
data-replication-service:
  runtime: python39
  memory: 4096MB
  timeout: 900s
  schedule: "0 1 * * *"

# 受眾媒合服務
audience-matching-service:
  runtime: python39
  memory: 4096MB
  timeout: 540s
  trigger: pubsub

# 統計分析服務
statistics-analysis-service:
  runtime: python39
  memory: 2048MB
  timeout: 300s
  trigger: pubsub
```

### Pub/Sub 主題設計

```yaml
topics:
  - name: "carrefour-data-replication-complete"
    description: "資料複製完成通知"

  - name: "carrefour-audience-matching-complete"
    description: "受眾媒合完成通知"

  - name: "carrefour-analysis-complete"
    description: "統計分析完成通知"

  - name: "carrefour-system-alerts"
    description: "系統告警通知"
```

### BigQuery 表格設計

```sql
-- 執行日誌表格
CREATE TABLE `tagtoo-tracking.carrefour_automation.execution_logs` (
  execution_id STRING,
  service_name STRING,
  start_time TIMESTAMP,
  end_time TIMESTAMP,
  status STRING,
  records_processed INT64,
  error_message STRING,
  metadata JSON
);

-- 監控指標表格
CREATE TABLE `tagtoo-tracking.carrefour_automation.monitoring_metrics` (
  metric_date DATE,
  service_name STRING,
  metric_name STRING,
  metric_value FLOAT64,
  created_at TIMESTAMP
);
```

## 📈 監控儀表板

### 關鍵指標 (KPIs)

1. **系統可用性**: 99.9% 目標
2. **資料處理量**: 每日處理用戶數
3. **處理延遲**: 端到端處理時間 < 2 小時
4. **資料品質**: 品質評分 > 95%
5. **錯誤率**: < 1%

### 儀表板組件

```yaml
dashboard_panels:
  - title: "每日處理概覽"
    metrics:
      - 處理用戶數
      - 生成標籤數
      - 執行時間

  - title: "系統健康狀態"
    metrics:
      - 服務可用性
      - 錯誤率
      - 資源使用率

  - title: "資料品質趨勢"
    metrics:
      - 品質評分
      - 完整性指標
      - 準確性指標
```

## 🚨 災難恢復

### 備份策略

1. **資料備份**: BigQuery 自動備份 + 手動快照
2. **配置備份**: Terraform 狀態檔案版本控制
3. **程式碼備份**: Git 版本控制

### 恢復程序

1. **服務故障**: 自動重啟 + 手動介入
2. **資料損壞**: 從備份恢復
3. **完全災難**: 跨區域恢復

## 🔄 部署策略

### CI/CD 流程

```yaml
stages:
  - name: "測試"
    steps:
      - 單元測試
      - 整合測試
      - 安全掃描

  - name: "部署測試環境"
    steps:
      - Terraform plan
      - Terraform apply
      - 煙霧測試

  - name: "部署生產環境"
    steps:
      - 人工審核
      - 藍綠部署
      - 監控驗證
```

### 版本管理

- **語義化版本**: v1.0.0
- **發布分支**: release/v1.0.0
- **熱修復**: hotfix/v1.0.1

## 📋 運維手冊

### 日常檢查項目

- [ ] 檢查每日執行狀態
- [ ] 驗證資料品質報告
- [ ] 檢查監控告警
- [ ] 檢查資源使用情況

### 故障處理流程

1. **接收告警** → 2. **初步診斷** → 3. **問題定位** → 4. **修復執行** → 5. **驗證恢復** → 6. **事後檢討**

### 效能調優

- **BigQuery 查詢優化**
- **Cloud Function 記憶體調整**
- **並行處理優化**
- **成本控制機制**

## 🔮 未來擴展

### 短期目標 (3個月)

- [ ] 完成基礎自動化部署
- [ ] 建立監控儀表板
- [ ] 實作告警機制

### 中期目標 (6個月)

- [ ] 支援多品牌擴展
- [ ] 實作機器學習優化
- [ ] 建立自動化測試

### 長期目標 (12個月)

- [ ] 實時資料處理
- [ ] 跨雲平台支援
- [ ] 智能化運維

---

**文檔版本**: v1.0.0
**最後更新**: 2025-08-25
**負責團隊**: Tagtoo Data Engineering Team
