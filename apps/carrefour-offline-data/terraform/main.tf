# 家樂福離線資料受眾媒合系統 Terraform 配置
# Cloud Functions 服務部署

terraform {
  required_version = ">= 1.12.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/carrefour-offline-data"
  }
}

# 本地變數
locals {
  service_full_name = "${var.service_name}-${var.environment}"

  common_labels = {
    app         = var.service_name
    environment = var.environment
    managed_by  = "terraform"
    team        = "data-team"
    project     = "carrefour-offline-data"
    repo        = "integrated-event"
  }
}

# 引用共用基礎設施的資料
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
  workspace = var.environment
}

# 本地變數 - 使用共用資源
locals {
  shared_outputs = data.terraform_remote_state.shared.outputs
}

provider "google" {
  project = local.shared_outputs.project_id
  region  = local.shared_outputs.region
}

# 建立 Cloud Storage bucket 用於 Cloud Function 原始碼
resource "google_storage_bucket" "function_source" {
  name     = "${local.service_full_name}-source"
  location = local.shared_outputs.region

  labels = local.common_labels

  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }

  versioning {
    enabled = true
  }
}

# 打包 Cloud Function 原始碼
data "archive_file" "function_source" {
  type        = "zip"
  output_path = "${path.module}/function-source.zip"
  source_dir  = var.function_source_dir

  excludes = [
    "__pycache__",
    "*.pyc",
    ".pytest_cache",
    "tests/",
    "terraform/",
    "docs/",
    "reports/",
    ".git/",
    "venv/",
    "*.md",
    "validation/",
    "*.log"
  ]
}

# 上傳 Function 原始碼到 Cloud Storage
resource "google_storage_bucket_object" "function_source" {
  name   = "source_${data.archive_file.function_source.output_md5}.zip"
  bucket = google_storage_bucket.function_source.name
  source = data.archive_file.function_source.output_path
}

# 移除 Pub/Sub Topic - 改用 HTTP 觸發

# Cloud Function (2nd Gen)
resource "google_cloudfunctions2_function" "carrefour_offline_data" {
  name     = local.service_full_name
  location = local.shared_outputs.region

  labels = local.common_labels

  build_config {
    runtime     = var.runtime
    entry_point = var.entry_point

    source {
      storage_source {
        bucket = google_storage_bucket.function_source.name
        object = google_storage_bucket_object.function_source.name
      }
    }
  }

  service_config {
    max_instance_count = var.max_instances
    min_instance_count = var.min_instances

    available_memory = var.memory
    available_cpu    = var.cpu
    timeout_seconds  = var.timeout

    environment_variables = merge(var.env_vars, {
      ENVIRONMENT          = var.environment
      PROJECT_ID           = local.shared_outputs.project_id
      GOOGLE_CLOUD_PROJECT = local.shared_outputs.project_id # 修復缺少的環境變數
      TARGET_PROJECT       = var.target_project_id
      SERVICE_NAME         = var.service_name
    })

    service_account_email = local.carrefour_service_account_email

    ingress_settings = "ALLOW_INTERNAL_ONLY"
  }

  # 移除 event_trigger - 改用 HTTP 觸發
}

# Cloud Scheduler Job (僅生產環境)
resource "google_cloud_scheduler_job" "carrefour_schedule" {
  count = var.enable_scheduler ? 1 : 0

  name        = "${local.service_full_name}-schedule"
  description = "每日自動執行家樂福離線資料受眾媒合系統"
  region      = local.shared_outputs.region
  schedule    = var.schedule_expression
  time_zone   = var.schedule_timezone

  http_target {
    uri         = google_cloudfunctions2_function.carrefour_offline_data.service_config[0].uri
    http_method = "POST"

    body = base64encode(jsonencode({
      execution_mode = "scheduled"
      days_back      = var.days_back
      dry_run        = false
      environment    = var.environment
    }))

    headers = {
      "Content-Type" = "application/json"
    }

    oidc_token {
      service_account_email = local.infrastructure_service_account_email
    }
  }

  depends_on = [
    google_cloudfunctions2_function.carrefour_offline_data
  ]
}

# IAM 權限配置已移至 iam.tf 檔案
