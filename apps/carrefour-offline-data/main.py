"""
家樂福受眾媒合系統 - Cloud Function 入口點

此 Cloud Function 每日自動執行家樂福線下購買資料與 Tagtoo 線上事件資料的媒合，
並根據購買商品分類建立受眾標籤，輸出到 BigQuery 表格供 ML 工作流程使用。

執行頻率: 每日凌晨 2:00 (台灣時間)
預估成本: $0.37 USD/天
預估執行時間: 2-3 分鐘
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any

import functions_framework
from flask import Request

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加 src 目錄到 Python 路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from analysis.carrefour_audience_matching import entity_optimized_run
except ImportError as e:
    logger.error(f"無法導入家樂福受眾媒合模組: {e}")
    entity_optimized_run = None


def _validate_environment() -> bool:
    """驗證執行環境"""
    required_env_vars = [
        'GOOGLE_CLOUD_PROJECT',
    ]

    missing_vars = []
    for var in required_env_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"缺少必要的環境變數: {missing_vars}")
        return False

    return True


def _parse_request(request: Request) -> Dict[str, Any]:
    """解析 HTTP 請求"""
    try:
        # 支援 Cloud Scheduler 的 POST 請求
        if request.method == 'POST':
            request_json = request.get_json(silent=True)
            if request_json:
                return request_json

        # 支援 GET 請求的查詢參數
        return dict(request.args)

    except Exception as e:
        logger.warning(f"解析請求失敗: {e}")
        return {}


def _execute_audience_matching(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """執行家樂福受眾媒合"""
    if not entity_optimized_run:
        raise ImportError("家樂福受眾媒合模組未正確載入")

    # 從請求中獲取參數，設定預設值
    days_back = int(request_data.get('days_back', 1))

    # 修復 dry_run 參數處理，支援布林值和字串
    dry_run_value = request_data.get('dry_run', False)
    if isinstance(dry_run_value, bool):
        dry_run = dry_run_value
    elif isinstance(dry_run_value, str):
        dry_run = dry_run_value.lower() in ('true', '1', 'yes')
    else:
        dry_run = False

    execution_mode = request_data.get('execution_mode', 'automated')

    logger.info(f"開始執行家樂福受眾媒合")
    logger.info(f"參數: days_back={days_back}, dry_run={dry_run}, execution_mode={execution_mode}")

    start_time = datetime.now()
    start_timestamp = time.time()

    # 執行時間監控閾值 (秒)
    WARNING_THRESHOLD = 240  # 4分鐘警告
    CRITICAL_THRESHOLD = 480  # 8分鐘嚴重警告

    try:
        # 執行 tagtoo_entity 優化版媒合
        entity_optimized_run(
            execution_mode=execution_mode,
            days_back=days_back,
            limit=None,  # 生產環境不限制記錄數
            dry_run=dry_run
        )

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # 執行時間狀態判斷
        if execution_time > CRITICAL_THRESHOLD:
            status = 'completed_with_critical_warning'
            logger.warning(f"⚠️ 執行時間過長警告: {execution_time:.1f}秒 > {CRITICAL_THRESHOLD}秒 (嚴重)")
        elif execution_time > WARNING_THRESHOLD:
            status = 'completed_with_warning'
            logger.warning(f"⚠️ 執行時間警告: {execution_time:.1f}秒 > {WARNING_THRESHOLD}秒")
        else:
            status = 'completed'

        result = {
            'execution_mode': execution_mode,
            'days_back': days_back,
            'dry_run': dry_run,
            'execution_time_seconds': execution_time,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'status': status,
            'performance_metrics': {
                'warning_threshold': WARNING_THRESHOLD,
                'critical_threshold': CRITICAL_THRESHOLD,
                'exceeded_warning': execution_time > WARNING_THRESHOLD,
                'exceeded_critical': execution_time > CRITICAL_THRESHOLD
            }
        }

        logger.info(f"家樂福受眾媒合執行完成，耗時 {execution_time:.1f} 秒 (狀態: {status})")
        return result

    except Exception as e:
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        logger.error(f"家樂福受眾媒合執行失敗: {e}")

        result = {
            'execution_mode': execution_mode,
            'days_back': days_back,
            'dry_run': dry_run,
            'execution_time_seconds': execution_time,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'status': 'failed',
            'error': str(e)
        }

        raise Exception(f"執行失敗: {e}") from e


@functions_framework.http
def main(request: Request) -> str:
    """
    Cloud Function 主要入口點

    支援的請求參數:
    - days_back: 回溯天數 (預設: 1)
    - dry_run: 是否為測試模式 (預設: false)
    - execution_mode: 執行模式 (預設: automated)

    Args:
        request: HTTP 請求物件

    Returns:
        str: JSON 格式的執行結果
    """
    try:
        logger.info("家樂福受眾媒合 Cloud Function 開始執行")

        # 驗證執行環境
        if not _validate_environment():
            return json.dumps({
                'status': 'error',
                'message': '執行環境驗證失敗'
            }), 500

        # 解析請求參數
        request_data = _parse_request(request)
        logger.info(f"請求參數: {request_data}")

        # 執行家樂福受眾媒合
        result = _execute_audience_matching(request_data)

        # 回傳成功結果
        response = {
            'status': 'success',
            'message': '家樂福受眾媒合執行完成',
            'result': result,
            'timestamp': datetime.now().isoformat()
        }

        logger.info("Cloud Function 執行成功")
        return json.dumps(response, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"Cloud Function 執行失敗: {str(e)}")

        error_response = {
            'status': 'error',
            'message': f'執行失敗: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }

        return json.dumps(error_response, ensure_ascii=False, indent=2), 500


# 本地測試支援
if __name__ == '__main__':
    # 本地測試模式
    import argparse

    parser = argparse.ArgumentParser(description='本地測試家樂福受眾媒合 Cloud Function')
    parser.add_argument('--days-back', type=int, default=1, help='回溯天數')
    parser.add_argument('--dry-run', action='store_true', help='測試模式')
    parser.add_argument('--execution-mode', default='manual', help='執行模式')

    args = parser.parse_args()

    # 模擬 HTTP 請求
    class MockRequest:
        def __init__(self, data):
            self.data = data
            self.method = 'POST'

        def get_json(self, silent=True):
            return self.data

    test_data = {
        'days_back': args.days_back,
        'dry_run': str(args.dry_run).lower(),
        'execution_mode': args.execution_mode
    }

    mock_request = MockRequest(test_data)
    result = main(mock_request)
    print(result)
