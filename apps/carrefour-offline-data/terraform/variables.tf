# 家樂福離線資料受眾媒合系統 Terraform 變數

variable "environment" {
  description = "環境名稱 (dev, staging, prod)"
  type        = string

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev, staging, 或 prod。"
  }
}

variable "service_name" {
  description = "服務名稱"
  type        = string
  default     = "carrefour-offline-data"
}

variable "deployment_version" {
  description = "部署版本 (通常是 git commit SHA)"
  type        = string
  default     = "latest"
}

# Cloud Function 配置
variable "runtime" {
  description = "Function 執行環境"
  type        = string
  default     = "python311"
}

variable "entry_point" {
  description = "Function 進入點"
  type        = string
  default     = "main"
}

variable "timeout" {
  description = "Function 執行逾時時間 (秒)"
  type        = number
  default     = 600
}

variable "memory" {
  description = "Function 記憶體限制"
  type        = string
  default     = "1024Mi"
}

variable "cpu" {
  description = "Function CPU 限制"
  type        = string
  default     = "1"
}

variable "max_instances" {
  description = "最大實例數"
  type        = number
  default     = 1
}

variable "min_instances" {
  description = "最小實例數"
  type        = number
  default     = 0
}

# 排程配置
variable "enable_scheduler" {
  description = "是否啟用排程器"
  type        = bool
  default     = true
}

variable "schedule_expression" {
  description = "排程表達式 (cron 格式)"
  type        = string
  default     = "0 2 * * *" # 每日台灣時間凌晨 2:00
}

variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}

# 業務邏輯配置
variable "days_back" {
  description = "回溯天數"
  type        = number
  default     = 1
}

variable "target_project_id" {
  description = "目標 BigQuery 專案 ID"
  type        = string
  default     = "tagtoo-ml-workflow"
}

variable "service_account_email" {
  description = "Service Account Email"
  type        = string
  default     = "<EMAIL>"
}

# 原始碼配置
variable "function_source_dir" {
  description = "Function 原始碼目錄路徑"
  type        = string
  default     = "../"
}

# 環境變數
variable "env_vars" {
  description = "Function 環境變數"
  type        = map(string)
  default = {
    EC_ID        = "715"
    MAX_COST_USD = "1.0"
  }
}

# IAM 配置
variable "enable_additional_logging" {
  description = "是否啟用額外的 Cloud Logging 權限"
  type        = bool
  default     = false
}

# 監控配置
variable "enable_monitoring" {
  description = "是否啟用監控和警報"
  type        = bool
  default     = true # 重新啟用監控系統
}

variable "notification_emails" {
  description = "接收警報通知的 Email 地址列表"
  type        = list(string)
  default     = ["<EMAIL>"]
}

variable "alert_thresholds" {
  description = "警報閾值配置"
  type = object({
    execution_time_seconds  = number
    memory_usage_percentage = number
    error_rate_percentage   = number
    daily_cost_usd          = number
    query_cost_usd          = number
  })
  default = {
    execution_time_seconds  = 300 # 5 分鐘
    memory_usage_percentage = 90  # 90%
    error_rate_percentage   = 5   # 5%
    daily_cost_usd          = 1.0 # $1 USD
    query_cost_usd          = 0.5 # $0.5 USD
  }
}
