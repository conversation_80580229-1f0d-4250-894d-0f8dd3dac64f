# 最近變更記錄

## 📊 概覽

本文檔記錄家樂福離線資料系統最近的重要變更，包括權限修復、架構升級和功能改進。

## 🔄 最近變更 (2025-09-01)

### Commit bd0c7a2: 修復 Cloud Scheduler 權限問題並採用共享基礎設施架構

#### 🚨 **問題背景**

- **問題**: Cloud Scheduler 連續幾天執行失敗，返回 HTTP 403 PERMISSION_DENIED
- **影響**: 家樂福受眾媒合系統無法自動執行
- **根本原因**: Cloud Function Gen2 缺少 `roles/run.invoker` 權限

#### 🔧 **主要變更**

##### 1. Service Account 架構升級

```yaml
變更前: <EMAIL>
變更後: <EMAIL>
```

**選擇理由**:

- 統一權限管理，提升可維護性
- 減少重複的 Service Account 配置
- 符合企業級最佳實務
- 自動繼承完整的 BigQuery、Storage 權限

##### 2. IAM 權限修復

**新增權限**:

```hcl
# Cloud Run Service 觸發權限 (修復關鍵問題)
resource "google_cloud_run_service_iam_binding" "invoker" {
  project  = local.shared_outputs.project_id
  location = local.shared_outputs.region
  service  = google_cloudfunctions2_function.carrefour_offline_data.name
  role     = "roles/run.invoker"
  members  = ["serviceAccount:${local.carrefour_service_account_email}"]
}
```

##### 3. 環境變數修復

**新增環境變數**:

```hcl
environment_variables = merge(var.env_vars, {
  ENVIRONMENT          = var.environment
  PROJECT_ID           = local.shared_outputs.project_id
  GOOGLE_CLOUD_PROJECT = local.shared_outputs.project_id  # 新增
  TARGET_PROJECT       = var.target_project_id
  SERVICE_NAME         = var.service_name
})
```

#### 📊 **測試結果**

- ✅ Cloud Scheduler 權限問題已解決
- ✅ 從 HTTP 403 進步到 HTTP 500 (應用層錯誤)
- ⚠️ 需要修復應用程式內部邏輯錯誤

#### 📁 **變更檔案**

- `apps/carrefour-offline-data/terraform/iam.tf` (64 insertions, 23 deletions)
- `apps/carrefour-offline-data/terraform/main.tf` (9 insertions, 1 deletion)

---

### Commit 615fa09: 修復 Cloud Function 布林值參數處理錯誤

#### 🚨 **問題背景**

- **問題**: Cloud Function 執行時發生 `'bool' object has no attribute 'lower'` 錯誤
- **影響**: HTTP 500 INTERNAL ERROR，系統無法正常處理資料
- **根本原因**: Cloud Scheduler 發送的 JSON 中 `dry_run` 是布林值，但程式碼假設為字串

#### 🔧 **主要變更**

##### 程式邏輯修復

**修改前**:

```python
dry_run = request_data.get('dry_run', 'false').lower() == 'true'
```

**修改後**:

```python
# 修復 dry_run 參數處理，支援布林值和字串
dry_run_value = request_data.get('dry_run', False)
if isinstance(dry_run_value, bool):
    dry_run = dry_run_value
elif isinstance(dry_run_value, str):
    dry_run = dry_run_value.lower() in ('true', '1', 'yes')
else:
    dry_run = False
```

#### 📊 **測試結果**

- ✅ Cloud Scheduler 成功觸發 (HTTP 200)
- ✅ Cloud Function 正常執行 (18秒完成)
- ✅ 成功處理 32,969 個用戶的受眾媒合
- ✅ 寫入 1,682,763 個標籤實例到 BigQuery

#### 📁 **變更檔案**

- `apps/carrefour-offline-data/main.py` (11 insertions, 1 deletion)

#### 🎯 **系統狀態**

- 家樂福離線資料系統完全正常運作
- 權限問題已解決，應用邏輯錯誤已修復
- 準備投入生產環境自動化執行

## 🔐 權限配置總結

### 當前 Service Account 配置

**主要 Service Account**: `<EMAIL>`

#### tagtoo-tracking 專案權限

- `roles/bigquery.dataEditor` - BigQuery 資料編輯
- `roles/bigquery.jobUser` - BigQuery Job 執行
- `roles/cloudfunctions.invoker` - Cloud Function 觸發
- `roles/run.invoker` - Cloud Run 服務觸發 (新增)
- `roles/cloudtasks.enqueuer` - Cloud Tasks 佇列
- `roles/datastore.user` - Datastore 存取
- `roles/firestore.serviceAgent` - Firestore 服務代理
- `roles/logging.logWriter` - 日誌寫入
- `roles/monitoring.metricWriter` - 監控指標寫入

#### tagtoo-ml-workflow 專案權限

- `roles/bigquery.admin` - BigQuery 完整管理
- `roles/bigquery.jobUser` - BigQuery Job 執行
- `roles/cloudfunctions.admin` - Cloud Function 管理
- `roles/storage.admin` - Storage 完整管理

### 資源級別 IAM 權限

#### Cloud Function IAM

```yaml
Resource: carrefour-offline-data-prod
Role: roles/cloudfunctions.invoker
Member: serviceAccount:<EMAIL>
```

#### Cloud Run Service IAM

```yaml
Resource: carrefour-offline-data-prod (Cloud Run Service)
Role: roles/run.invoker
Member: serviceAccount:<EMAIL>
```

## 🚀 部署流程

### 標準部署步驟

1. **程式碼變更**

   ```bash
   # 修改相關檔案
   git add <changed-files>
   git commit -m "fix(carrefour): <description>"
   ```

2. **Terraform 部署**

   ```bash
   cd apps/carrefour-offline-data/terraform
   terraform plan -var-file="environments/prod.tfvars"
   terraform apply
   ```

3. **功能驗證**

   ```bash
   # 手動觸發測試
   gcloud scheduler jobs run carrefour-offline-data-prod-schedule \
     --location=asia-east1

   # 檢查執行結果
   gcloud logging read 'resource.type="cloud_scheduler_job" AND
     resource.labels.job_id="carrefour-offline-data-prod-schedule"' \
     --limit=2
   ```

### 回滾程序

如果部署出現問題，可以使用以下步驟回滾：

```bash
# 檢查 Terraform 狀態
terraform show

# 回滾到上一個版本
git revert <commit-hash>
terraform plan -var-file="environments/prod.tfvars"
terraform apply
```

## 📊 監控和警報

### 自動監控指標

1. **Cloud Scheduler 執行狀態**

   - 警報閾值: 連續 2 次失敗
   - 通知頻道: <EMAIL>

2. **Cloud Function 效能監控**

   - 執行時間: > 300 秒警報
   - 記憶體使用: > 90% 警報
   - 錯誤率: > 5% 警報

3. **BigQuery 成本監控**
   - 單次查詢成本: > $0.5 警報
   - 日總成本: > $1.0 警報

### 手動檢查指令

```bash
# 檢查系統健康狀態
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
  --location=asia-east1 --format="yaml(status,lastAttemptTime)"

# 檢查處理結果
bq query --use_legacy_sql=false \
  "SELECT COUNT(*) as total_records,
   MAX(created_at) as latest_update
   FROM \`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)\`"
```

## 📞 支援聯絡

**系統負責人**: Frank Zheng (<EMAIL>)
**技術支援**: Data Team
**緊急聯絡**: 透過 Google Cloud Monitoring 警報系統

---

**文檔建立**: 2025-09-01
**最後更新**: 2025-09-01
**下次審查**: 2025-12-01
