# 觸發流程和權限詳解

## 📊 系統觸發流程概覽

家樂福離線資料系統採用 Cloud Scheduler → Cloud Function → BigQuery 的完整自動化流程。

## 🔄 完整觸發流程

### 1. 自動化觸發 (生產環境)

```mermaid
sequenceDiagram
    participant CS as Cloud Scheduler
    participant CF as Cloud Function
    participant B<PERSON> as BigQuery
    participant ML as ML Workflow

    Note over CS: 每日 02:00 (Asia/Taipei)
    CS->>+CF: HTTP POST with OIDC Token
    Note over CS,CF: Service Account: integrated-event-prod

    CF->>CF: 驗證環境變數
    CF->>CF: 解析請求參數

    CF->>+BQ: 查詢家樂福交易資料
    Note over BQ: tagtoo-tracking.event_prod
    BQ-->>-CF: 返回交易記錄

    CF->>CF: 執行受眾媒合邏輯
    CF->>CF: 生成受眾標籤

    CF->>+ML: 寫入受眾媒合結果
    Note over ML: tagtoo-ml-workflow.tagtoo_export_results
    ML-->>-CF: 確認寫入成功

    CF-->>-CS: HTTP 200 (成功)
```

### 2. 手動觸發

```bash
# 手動觸發 Cloud Scheduler
gcloud scheduler jobs run carrefour-offline-data-prod-schedule \
  --location=asia-east1

# 直接呼叫 Cloud Function (需要認證)
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  -H "Content-Type: application/json" \
  -d '{"execution_mode":"manual","days_back":1,"dry_run":false}' \
  https://carrefour-offline-data-prod-shiddvur4q-de.a.run.app/
```

## 🔐 詳細權限驗證流程

### 階段 1: Cloud Scheduler 權限驗證

```yaml
觸發者: Cloud Scheduler Job
Service Account: <EMAIL>
權限檢查:
  1. OIDC Token 生成權限: ✅
     - 需要: roles/iam.serviceAccountTokenCreator (隱含)
     - 驗證: gcloud auth print-identity-token

  2. Cloud Function 觸發權限: ✅
     - 需要: roles/cloudfunctions.invoker
     - 資源: carrefour-offline-data-prod
     - 驗證: HTTP POST 到 Cloud Function 端點
```

### 階段 2: Cloud Function Gen2 權限驗證

```yaml
執行者: Cloud Function Runtime
Service Account: <EMAIL>
權限檢查:
  1. Cloud Run Service 觸發權限: ✅
     - 需要: roles/run.invoker
     - 資源: carrefour-offline-data-prod (Cloud Run Service)
     - 驗證: Cloud Function 成功啟動

  2. 應用程式執行權限: ✅
     - 需要: 基本執行環境權限
     - 驗證: Python 應用程式正常載入
```

### 階段 3: BigQuery 資料存取權限驗證

```yaml
執行者: Cloud Function Application
Service Account: <EMAIL>

來源資料讀取 (tagtoo-tracking):
  1. 資料表存取權限: ✅
     - 需要: roles/bigquery.dataEditor
     - 資源: tagtoo-tracking.event_prod.*
     - 驗證: SELECT 查詢成功執行

  2. 查詢執行權限: ✅
     - 需要: roles/bigquery.jobUser
     - 驗證: BigQuery Job 成功建立和執行

目標資料寫入 (tagtoo-ml-workflow):
  1. 跨專案存取權限: ✅
     - 需要: roles/bigquery.admin
     - 專案: tagtoo-ml-workflow
     - 驗證: 跨專案查詢和寫入成功

  2. 資料表寫入權限: ✅
     - 需要: roles/bigquery.dataEditor (包含在 admin 中)
     - 資源: tagtoo_export_results.special_lta_temp_for_update_*
     - 驗證: INSERT 操作成功執行
```

## 📋 權限配置檢查清單

### ✅ 必要權限檢查

#### 1. Cloud Scheduler 相關

- [ ] Cloud Scheduler Job 存在且啟用
- [ ] OIDC Token 配置正確
- [ ] Service Account Email 設定正確
- [ ] 排程表達式和時區設定正確

#### 2. Cloud Function 相關

- [ ] Cloud Function 部署成功
- [ ] Service Account 配置正確
- [ ] 環境變數設定完整
- [ ] Ingress 設定為 ALLOW_INTERNAL_ONLY

#### 3. IAM 權限相關

- [ ] Cloud Function IAM: roles/cloudfunctions.invoker
- [ ] Cloud Run Service IAM: roles/run.invoker
- [ ] BigQuery 讀取權限: roles/bigquery.dataEditor
- [ ] BigQuery 執行權限: roles/bigquery.jobUser
- [ ] 跨專案寫入權限: roles/bigquery.admin

#### 4. 資料表存取相關

- [ ] 來源資料表存在且可讀取
- [ ] 目標資料表存在且可寫入
- [ ] 跨專案網路連線正常

## 🚨 常見問題和解決方案

### 問題 1: HTTP 403 PERMISSION_DENIED

**症狀**: Cloud Scheduler 觸發失敗，返回 403 錯誤

**可能原因**:

1. 缺少 `roles/run.invoker` 權限
2. OIDC Token 配置錯誤
3. Service Account 不存在或被停用

**診斷步驟**:

```bash
# 檢查 Cloud Run Service IAM
gcloud run services get-iam-policy carrefour-offline-data-prod \
  --region=asia-east1

# 檢查 Service Account 狀態
gcloud iam service-accounts describe \
  <EMAIL>

# 檢查 OIDC Token 配置
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
  --location=asia-east1 --format="yaml(httpTarget.oidcToken)"
```

**解決方案**:

```bash
# 重新部署 Terraform 配置
cd apps/carrefour-offline-data/terraform
terraform plan -var-file="environments/prod.tfvars"
terraform apply
```

### 問題 2: HTTP 500 INTERNAL ERROR

**症狀**: Cloud Function 觸發成功但執行失敗

**可能原因**:

1. 應用程式邏輯錯誤
2. BigQuery 權限不足
3. 環境變數缺失
4. 資料表不存在

**診斷步驟**:

```bash
# 檢查 Cloud Function 日誌
gcloud logging read 'resource.type="cloud_run_revision" AND
  resource.labels.service_name="carrefour-offline-data-prod"' \
  --limit=10 --format="yaml(timestamp,severity,textPayload)"

# 檢查環境變數
gcloud functions describe carrefour-offline-data-prod \
  --region=asia-east1 --gen2 \
  --format="yaml(serviceConfig.environmentVariables)"

# 檢查目標資料表
bq show tagtoo-ml-workflow:tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)
```

### 問題 3: BigQuery 權限錯誤

**症狀**: 無法讀取來源資料或寫入目標資料

**診斷步驟**:

```bash
# 檢查跨專案權限
gcloud projects get-iam-policy tagtoo-ml-workflow \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 測試 BigQuery 存取
bq query --use_legacy_sql=false \
  "SELECT COUNT(*) FROM \`tagtoo-tracking.event_prod.tagtoo_event\` LIMIT 1"
```

## 📊 監控和警報

### 自動監控指標

1. **Cloud Scheduler 執行狀態**

   - 成功/失敗率監控
   - 執行時間監控
   - 警報閾值: 連續 2 次失敗

2. **Cloud Function 效能監控**

   - 執行時間: > 300 秒警報
   - 記憶體使用: > 90% 警報
   - 錯誤率: > 5% 警報

3. **BigQuery 成本監控**
   - 單次查詢成本: > $0.5 警報
   - 日總成本: > $1.0 警報

### 手動檢查指令

```bash
# 檢查最近的執行狀態
gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
  --location=asia-east1 --format="yaml(status,lastAttemptTime)"

# 檢查 Cloud Function 指標
gcloud logging read 'resource.type="cloud_scheduler_job" AND
  resource.labels.job_id="carrefour-offline-data-prod-schedule"' \
  --limit=5 --format="yaml(timestamp,severity,jsonPayload.status)"

# 檢查處理結果
bq query --use_legacy_sql=false \
  "SELECT COUNT(*) as total_records,
   MAX(created_at) as latest_update
   FROM \`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)\`"
```

## 🔧 維護和更新

### 定期維護任務

1. **每週檢查** (週一)

   - 檢查過去一週的執行狀態
   - 確認資料處理量正常
   - 檢查成本使用情況

2. **每月檢查** (月初)

   - 審查 Service Account 權限
   - 更新文檔和操作手冊
   - 檢查監控警報設定

3. **每季檢查** (季初)
   - 全面安全審查
   - 效能最佳化評估
   - 災難恢復測試

### 緊急聯絡

**系統負責人**: Frank Zheng (<EMAIL>)
**技術支援**: Data Team
**緊急聯絡**: 透過 Google Cloud Monitoring 警報系統

---

**最後更新**: 2025-09-01
**文檔版本**: v1.0
**下次審查**: 2025-12-01
