# 家樂福離線資料受眾媒合系統

> Last Updated: 2025-09-01
> Version: 7.0.0 (架構分離設計版)

## 📋 專案概述

本專案實現家樂福線下購買資料與 Tagtoo 線上事件資料的自動化媒合，根據購買商品分類建立受眾標籤，支援 ML 工作流程的精準行銷。採用 Service Account 架構分離設計，確保客戶資料隔離和安全性。

**資料來源**：`tagtoo-tracking.event_prod` (tagtoo*event, carrefour_offline_transaction_day, tagtoo_entity)
**資料目標**：`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update*\*`
**執行頻率**：每日凌晨 2:00 (台灣時間) 自動執行
**預估成本**：$0.37 USD/天
**預估執行時間**：2-3 分鐘
**專案狀態**：✅ 生產環境運行中，✅ 架構分離設計，✅ 完整監控和警報

## 🏗️ 系統架構

**部署方式**：Google Cloud Platform 全託管服務
**觸發機制**：Cloud Scheduler + Cloud Function Gen2 + BigQuery
**架構設計**：Service Account 架構分離設計
**監控系統**：完整的警報和日誌監控

### Service Account 架構分離

```yaml
架構分離設計:
  基礎設施層:
    Service Account: <EMAIL>
    職責: Cloud Scheduler 觸發和基礎設施管理
    權限: Cloud Function/Run 觸發權限

  專案執行層:
    Service Account: <EMAIL>
    職責: Cloud Function 執行和資料處理
    權限: BigQuery 資料存取、跨專案寫入權限
```

## 🔄 自動化執行流程

```mermaid
sequenceDiagram
    participant CS as Cloud Scheduler
    participant CF as Cloud Function Gen2
    participant BQ as BigQuery
    participant ML as ML Workflow

    Note over CS: 基礎設施層 SA
    CS->>+CF: HTTP POST with OIDC Token
    Note over CS,CF: integrated-event-prod SA

    Note over CF: 專案執行層 SA
    CF->>CF: 切換到 carrefour-tagtoo-bigquery-view

    CF->>+BQ: 查詢家樂福交易資料
    Note over BQ: tagtoo-tracking.event_prod
    BQ-->>-CF: 返回交易記錄

    CF->>CF: 執行受眾媒合邏輯
    CF->>CF: 生成受眾標籤

    CF->>+ML: 寫入受眾媒合結果
    Note over ML: tagtoo-ml-workflow.tagtoo_export_results
    ML-->>-CF: 確認寫入成功

    CF-->>-CS: HTTP 200 (成功)
```

## 🚀 系統管理

### 監控和狀態檢查

1. **檢查系統狀態**：

   ```bash
   # 檢查 Cloud Scheduler 狀態
   gcloud scheduler jobs describe carrefour-offline-data-prod-schedule \
     --location=asia-east1

   # 檢查 Cloud Function 狀態
   gcloud functions describe carrefour-offline-data-prod \
     --region=asia-east1 --gen2

   # 檢查最近執行日誌
   gcloud logging read 'resource.type="cloud_scheduler_job" AND
     resource.labels.job_id="carrefour-offline-data-prod-schedule"' \
     --limit=5
   ```

2. **手動觸發執行**：

   ```bash
   # 手動觸發 Cloud Scheduler
   gcloud scheduler jobs run carrefour-offline-data-prod-schedule \
     --location=asia-east1

   # 直接呼叫 Cloud Function (需要認證)
   curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
     -H "Content-Type: application/json" \
     -d '{"execution_mode":"manual","days_back":1,"dry_run":false}' \
     https://carrefour-offline-data-prod-shiddvur4q-de.a.run.app/
   ```

3. **檢查處理結果**：

   ```bash
   # 檢查今日處理結果
   bq query --use_legacy_sql=false \
     "SELECT COUNT(*) as total_records, MAX(created_at) as latest_update
      FROM \`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)\`"

   # 檢查 Cloud Function 執行日誌
   gcloud logging read 'resource.type="cloud_run_revision" AND
     resource.labels.service_name="carrefour-offline-data-prod"' \
     --limit=10
   ```

## 🔐 權限配置

### Service Account 權限詳情

#### 基礎設施層 Service Account

- **Email**: `<EMAIL>`
- **用途**: Cloud Scheduler 觸發和基礎設施管理
- **權限**:
  - `roles/cloudfunctions.invoker` - Cloud Function 觸發
  - `roles/run.invoker` - Cloud Run Service 觸發

#### 專案執行層 Service Account

- **Email**: `<EMAIL>`
- **用途**: Cloud Function 執行和資料處理
- **權限**:
  - `roles/bigquery.dataEditor` - BigQuery 資料編輯 (本專案)
  - `roles/bigquery.jobUser` - BigQuery Job 執行 (本專案)
  - `roles/storage.objectUser` - Storage 物件存取
  - `roles/bigquery.dataEditor` - BigQuery 資料編輯 (ML Workflow 專案)
  - `roles/bigquery.jobUser` - BigQuery Job 執行 (ML Workflow 專案)

### 權限驗證

```bash
# 檢查基礎設施層 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 檢查專案執行層 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# 檢查跨專案權限
gcloud projects get-iam-policy tagtoo-ml-workflow \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

## 🛠️ 開發環境設定

### 本地開發和測試

```bash
# 啟動虛擬環境
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt

# 設定環境變數
export GOOGLE_CLOUD_PROJECT=tagtoo-tracking
export ENVIRONMENT=dev

# 本地測試執行
python main.py --days-back 1 --dry-run --execution-mode manual
```

- 📖 **跨平台設定**：參考 [`docs/CROSS_PLATFORM_SETUP.md`](docs/CROSS_PLATFORM_SETUP.md)
- 📋 **操作手冊**：參考 [`docs/OPERATIONS_MANUAL.md`](docs/OPERATIONS_MANUAL.md)
- ✅ **執行檢查清單**：參考 [`docs/EXECUTION_CHECKLIST.md`](docs/EXECUTION_CHECKLIST.md)

## 🎯 主要功能

### 核心系統（src/）

1. **智慧資料複製**：支援 1500+ 萬筆資料的高效複製
2. **四層次驗證**：從基本統計到深度結構驗證
3. **Schema 一致性檢查**：確保來源與目標表格完全一致
4. **監控日誌系統**：結構化日誌和效能監控
5. **環境自動化設定**：一鍵環境配置和依賴安裝
6. **整合執行腳本**：完整流程自動化

### 輔助工具（tools/）

- 規格驗證工具（v1-v3）
- 表格檢查和診斷工具

### 完整文檔（docs/）

- 操作手冊和執行檢查清單
- 技術策略和權限申請文件
- 開發記錄和驗證規格

## 🛠️ 技術架構

- **Python 3.9+**：主要開發語言
- **Google Cloud BigQuery**：資料存取和查詢
- **Service Account**：`<EMAIL>`
- **認證方式**：Service Account Impersonation
- **效能設計**：2,000-5,000 行/秒，成本 < $1 USD

## 🚀 快速開始

### 環境設定

1. **自動化環境設定**：

   ```bash
   python3 src/setup_environment.py
   ```

2. **手動環境設定**：

   ```bash
   # 建立虛擬環境
   python3 -m venv venv
   source venv/bin/activate

   # 安裝依賴
   pip install -r requirements.txt

   # 設定認證
   source config/set_env.sh
   ```

### 執行完整流程（權限開通後）

```bash
# 一鍵執行完整流程
python3 src/run_full_pipeline.py

# 或分步執行
python3 src/schema_validator.py      # Schema 驗證
python3 src/copy_carrefour_data.py   # 資料複製
python3 src/data_validator.py        # 資料驗證
```

## 📁 專案結構

```text
carrefour-offline-data/
├── README.md                          # 專案說明文檔
├── main.py                            # Cloud Function 入口點 ⭐
├── requirements.txt                   # Python 依賴套件
├── 📁 src/                            # 核心系統程式碼
│   └── analysis/
│       └── carrefour_audience_matching.py  # 受眾媒合核心邏輯
│
├── 📁 terraform/                      # 基礎設施即程式碼
│   ├── main.tf                        # 主要 Terraform 配置
│   ├── variables.tf                   # 變數定義
│   ├── outputs.tf                     # 輸出定義
│   ├── iam.tf                         # IAM 權限配置 (架構分離)
│   └── environments/                  # 環境特定配置
│       ├── dev.tfvars                 # 開發環境
│       ├── staging.tfvars             # 測試環境
│       └── prod.tfvars                # 生產環境
│
├── 📁 docs/                           # 專案文檔
│   ├── architecture/                  # 架構設計文檔
│   │   ├── service-account-permissions.md
│   │   └── automation_architecture.md
│   ├── deployment/                    # 部署相關文檔
│   │   ├── terraform_deployment_guide.md
│   │   └── terraform_deployment_requirements.md
│   └── operations/                    # 運維操作文檔
│       └── trigger-flow-and-permissions.md
```

## 🔧 關鍵檔案說明

### 🌟 核心檔案

- **`main.py`**：Cloud Function 入口點，處理 HTTP 請求和參數解析
- **`src/analysis/carrefour_audience_matching.py`**：受眾媒合核心邏輯
- **`terraform/iam.tf`**：Service Account 架構分離配置
- **`terraform/main.tf`**：Cloud Function 和 Cloud Scheduler 配置

### 重要文檔

- **`docs/architecture/service-account-permissions.md`**：權限配置詳解
- **`docs/operations/trigger-flow-and-permissions.md`**：觸發流程和權限驗證
- **`docs/deployment/terraform_deployment_guide.md`**：部署指南

## 📊 系統效能指標

### 當前運行狀況

- **執行時間**：平均 2-3 分鐘
- **處理用戶數**：約 32,000+ 個用戶
- **生成標籤數**：約 1,680,000+ 個標籤實例
- **查詢成本**：約 $0.18 USD/次
- **成功率**：99.9%+
- **記憶體使用**：< 800Mi
- **CPU 使用**：< 1 vCPU

### 監控指標

```yaml
關鍵指標:
  執行成功率: > 95%
  執行時間: < 180 秒
  查詢成本: < $0.2 USD
  記憶體使用: < 800Mi
  錯誤率: < 1%
```

## 🚨 故障排除

### 常見問題

#### 1. HTTP 403 PERMISSION_DENIED

**症狀**：Cloud Scheduler 觸發失敗
**原因**：缺少 Cloud Run Service 觸發權限
**解決方案**：

```bash
# 檢查 IAM 權限配置
terraform plan -var-file="environments/prod.tfvars"
terraform apply -var-file="environments/prod.tfvars"
```

#### 2. BigQuery 存取被拒

**症狀**：無法讀取或寫入 BigQuery 資料
**原因**：Service Account 權限不足
**解決方案**：

```bash
# 檢查專案執行層 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"
```

#### 3. Cloud Function 執行超時

**症狀**：執行時間超過 10 分鐘
**原因**：資料量過大或查詢效率問題
**解決方案**：檢查 BigQuery 查詢效能和成本

### 🔔 監控和警報系統 (2025-09-01 最新配置)

#### **警報策略覆蓋範圍**

- ✅ **執行時間監控**：超過 300 秒（5 分鐘）觸發警報
- ✅ **錯誤率監控**：任何執行失敗立即觸發警報（5 分鐘檢測）
- ✅ **記憶體使用率監控**：超過 90% 記憶體使用率觸發警報
- ✅ **執行頻率異常監控**：每日執行超過 1 次觸發警報
- ✅ **Cloud Scheduler 失敗監控**：調度失敗立即觸發警報

#### **通知配置**

- **通知頻道架構**：專案隔離設計，符合企業級最佳實務
- **當前頻道**：`家樂福離線資料系統 - prod - <EMAIL>`
- **Email 通知**：<EMAIL>（專項專用頻道，完全隔離）
- **警報策略數量**：5 個已部署並驗證的監控項目
- **用戶標籤**：project, environment, purpose, owner（便於管理分類）
- **多收件人支援**：可透過建立多個通知頻道實現（詳見技術文檔）

#### **監控技術細節**

- **Cloud Monitoring**：基於原生 Cloud Function 指標
- **Cloud Logging**：詳細的執行日誌和錯誤追蹤
- **監控間隔**：300 秒（平衡檢測速度與成本效益）
- **警報閾值**：基於實際運行數據優化設定

#### **通知頻道架構設計**

```yaml
架構原則:
  專案隔離: 每個專案建立專用通知頻道
  標準化命名: "{專案名稱} - {環境} - {收件人}"
  用戶標籤: project, environment, purpose, owner

當前配置:
  頻道名稱: "家樂福離線資料系統 - prod - <EMAIL>"
  支援擴展: 可添加多個收件人（<EMAIL> 等）
  成本影響: 零成本（通知頻道完全免費）

未來擴展:
  多收件人: 在 notification_emails 變數中添加新 email
  跨專案: 每個新專案建立專用通知頻道
  通知方式: 支援 Email、SMS、Slack、PagerDuty 等
```

## 🔄 部署和更新

### Terraform 部署

```bash
# 進入 Terraform 目錄
cd terraform

# 檢查配置
terraform plan -var-file="environments/prod.tfvars"

# 部署更新
terraform apply -var-file="environments/prod.tfvars"
```

### 程式碼更新

```bash
# 更新程式碼後，Terraform 會自動重新部署 Cloud Function
terraform apply -var-file="environments/prod.tfvars"
```

## 📚 相關文檔

### 架構設計

- [Service Account 權限配置詳解](docs/architecture/service-account-permissions.md)
- [自動化架構設計](docs/architecture/automation_architecture.md)

### 部署和運維

- [Terraform 部署指南](docs/deployment/terraform_deployment_guide.md)
- [觸發流程和權限驗證](docs/operations/trigger-flow-and-permissions.md)

### 企業級架構標準

- [Service Account 架構分離設計原則](../../docs/architecture/service-account-separation-design.md)
- [安全性最佳實務指南](../../docs/architecture/security-best-practices.md)

## 📞 支援聯絡

**系統負責人**：Frank Zheng (<EMAIL>)
**技術支援**：Data Engineering Team
**緊急聯絡**：透過 Google Cloud Monitoring 警報系統

---

**最後更新**：2025-09-01
**版本**：v7.0 (架構分離設計版)
**狀態**：✅ 生產環境運行中，✅ 架構分離設計完成
