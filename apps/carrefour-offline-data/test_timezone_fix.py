#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
時區修正驗證測試
驗證 _calculate_target_date() 方法在不同時區環境下的行為
"""

import os
import sys
from datetime import datetime, timezone
import pytz
from unittest.mock import patch

# 添加 src 目錄到 Python 路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from analysis.carrefour_audience_matching import CarrefourAudienceMatching


def test_timezone_fix():
    """測試時區修正是否正確"""
    print("=== 家樂福時區修正驗證測試 ===\n")

    # 模擬 HKT 凌晨 02:00 的情況 (2025-09-02 02:00 HKT = 2025-09-01 18:00 UTC)
    hkt_time = datetime(2025, 9, 2, 2, 0, 0, tzinfo=pytz.timezone('Asia/Hong_Kong'))
    utc_time = hkt_time.astimezone(timezone.utc)
    taipei_time = hkt_time.astimezone(pytz.timezone('Asia/Taipei'))

    print(f"模擬執行時間:")
    print(f"  HKT:    {hkt_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"  UTC:    {utc_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"  Taipei: {taipei_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print()

    # 測試修正前的行為 (使用系統時間，假設是 UTC)
    print("修正前的行為 (使用系統 UTC 時間):")
    with patch('analysis.carrefour_audience_matching.datetime') as mock_datetime:
        mock_datetime.now.return_value = utc_time.replace(tzinfo=None)  # 模擬沒有時區的 datetime.now()
        mock_datetime.strftime = datetime.strftime

        # 這會導致錯誤的日期計算
        target_date_old = utc_time.replace(tzinfo=None).strftime('%Y%m%d')
        print(f"  目標日期: {target_date_old} (錯誤 - 應該是 20250902)")

    print()

    # 測試修正後的行為
    print("修正後的行為 (使用台灣時區):")
    with patch('analysis.carrefour_audience_matching.datetime') as mock_datetime:
        # 模擬 datetime.now(taipei_tz) 返回正確的台灣時間
        mock_datetime.now.side_effect = lambda tz=None: taipei_time if tz else utc_time.replace(tzinfo=None)
        mock_datetime.strftime = datetime.strftime

        # 初始化系統 (自動化模式)
        matcher = CarrefourAudienceMatching(execution_mode="automated")
        target_date_new = matcher._calculate_target_date()
        print(f"  目標日期: {target_date_new} (正確 - 使用台灣時間當日)")

    print()

    # 驗證結果
    expected_date = "20250902"
    if target_date_new == expected_date:
        print("✅ 時區修正成功！")
        print(f"   在 HKT 凌晨 02:00 執行時，正確使用 {expected_date} 作為目標日期")
    else:
        print("❌ 時區修正失敗！")
        print(f"   期望: {expected_date}, 實際: {target_date_new}")

    print()

    # 測試手動模式
    print("測試手動執行模式:")
    with patch('analysis.carrefour_audience_matching.datetime') as mock_datetime:
        mock_datetime.now.side_effect = lambda tz=None: taipei_time if tz else utc_time.replace(tzinfo=None)
        mock_datetime.strftime = datetime.strftime

        matcher_manual = CarrefourAudienceMatching(execution_mode="manual")
        target_date_manual = matcher_manual._calculate_target_date()
        expected_manual = "20250903"  # 手動模式應該是明天

        print(f"  目標日期: {target_date_manual}")
        if target_date_manual == expected_manual:
            print("✅ 手動模式時區處理正確")
        else:
            print(f"❌ 手動模式時區處理錯誤，期望: {expected_manual}")


def test_execution_id_timezone():
    """測試執行 ID 生成的時區一致性"""
    print("\n=== 執行 ID 時區測試 ===")

    # 模擬台灣時間
    taipei_time = datetime(2025, 9, 2, 2, 30, 45, tzinfo=pytz.timezone('Asia/Taipei'))

    with patch('analysis.carrefour_audience_matching.datetime') as mock_datetime:
        mock_datetime.now.side_effect = lambda tz=None: taipei_time if tz else taipei_time.replace(tzinfo=None)
        mock_datetime.strftime = datetime.strftime

        matcher = CarrefourAudienceMatching(execution_mode="automated")
        execution_id = matcher._generate_execution_id()

        print(f"生成的執行 ID: {execution_id}")

        # 檢查是否包含正確的台灣時間戳
        expected_timestamp = "20250902_023045"
        if expected_timestamp in execution_id:
            print("✅ 執行 ID 使用正確的台灣時間")
        else:
            print(f"❌ 執行 ID 時間戳錯誤，期望包含: {expected_timestamp}")


if __name__ == "__main__":
    test_timezone_fix()
    test_execution_id_timezone()

    print("\n=== 測試完成 ===")
    print("修正摘要:")
    print("1. _calculate_target_date() 現在使用 Asia/Taipei 時區")
    print("2. _generate_execution_id() 現在使用 Asia/Taipei 時區")
    print("3. 查詢日期範圍計算現在使用 Asia/Taipei 時區")
    print("4. 確保在 HKT 凌晨執行時使用正確的當日日期")
